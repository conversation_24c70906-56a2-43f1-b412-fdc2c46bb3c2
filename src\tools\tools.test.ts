import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { toolRegistry } from './index';
import { ExecutionContext, ProjectContext, ProjectType } from '../types';
import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import { randomBytes } from 'crypto';

// Simple ID generator to replace nanoid
function generateId(): string {
  return randomBytes(8).toString('hex');
}

describe('Tool System', () => {
  let mockContext: ExecutionContext;
  let tempDir: string;

  beforeEach(async () => {
    // Create temporary directory for tests
    tempDir = path.join(os.tmpdir(), `agentic-test-${generateId()}`);
    await fs.ensureDir(tempDir);

    // Create mock project context
    const mockProjectContext: ProjectContext = {
      root: tempDir,
      type: 'nodejs' as ProjectType,
      structure: {
        directories: [],
        files: [],
        totalFiles: 0,
        totalDirectories: 0,
        lastIndexed: new Date(),
      },
      dependencies: [],
      configuration: {},
    };

    // Create mock execution context
    mockContext = {
      sessionId: generateId(),
      workingDirectory: tempDir,
      environment: process.env as Record<string, string>,
      projectContext: mockProjectContext,
      agent: {} as any,
    };
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  describe('Tool Registry', () => {
    it('should register all tools', () => {
      const tools = toolRegistry.getAllTools();
      expect(tools.length).toBeGreaterThan(0);
      
      // Check that essential tools are registered
      const toolNames = toolRegistry.getToolNames();
      expect(toolNames).toContain('read_file');
      expect(toolNames).toContain('write_file');
      expect(toolNames).toContain('execute_command');
      expect(toolNames).toContain('get_project_context');
      expect(toolNames).toContain('analyze_code');
      expect(toolNames).toContain('execute_parallel_tools');
    });

    it('should retrieve tools by name', () => {
      const readFileTool = toolRegistry.getTool('read_file');
      expect(readFileTool).toBeDefined();
      expect(readFileTool?.name).toBe('read_file');
    });

    it('should return undefined for non-existent tools', () => {
      const nonExistentTool = toolRegistry.getTool('non_existent_tool');
      expect(nonExistentTool).toBeUndefined();
    });
  });

  describe('File Operations', () => {
    it('should write and read files', async () => {
      const writeFileTool = toolRegistry.getTool('write_file');
      const readFileTool = toolRegistry.getTool('read_file');
      
      expect(writeFileTool).toBeDefined();
      expect(readFileTool).toBeDefined();

      const testFile = path.join(tempDir, 'test.txt');
      const testContent = 'Hello, World!';

      // Write file
      const writeResult = await writeFileTool!.execute({
        filePath: testFile,
        content: testContent,
        overwrite: true,
      }, mockContext);

      expect(writeResult.success).toBe(true);

      // Read file
      const readResult = await readFileTool!.execute({
        filePath: testFile,
      }, mockContext);

      expect(readResult.success).toBe(true);
      expect((readResult.data as { content?: string })?.content).toBe(testContent);
    });

    it('should search files with patterns', async () => {
      const searchFilesTool = toolRegistry.getTool('search_files');
      expect(searchFilesTool).toBeDefined();

      // Create test files
      await fs.writeFile(path.join(tempDir, 'test1.js'), 'console.log("test1");');
      await fs.writeFile(path.join(tempDir, 'test2.ts'), 'console.log("test2");');
      await fs.writeFile(path.join(tempDir, 'readme.md'), '# Test Project');

      const searchResult = await searchFilesTool!.execute({
        pattern: '*.js',
        directory: tempDir,
      }, mockContext);

      expect(searchResult.success).toBe(true);
      const searchData = searchResult.data as { results?: unknown[] };
      expect(searchData?.results).toBeDefined();
      expect(searchData?.results?.length).toBeGreaterThan(0);
    });
  });

  describe('Code Analysis', () => {
    it('should analyze code complexity', async () => {
      const analyzeCodeTool = toolRegistry.getTool('analyze_code');
      expect(analyzeCodeTool).toBeDefined();

      // Create a test file with some complexity
      const testFileName = 'complex.js';
      const testFile = path.join(tempDir, testFileName);
      const complexCode = `
function complexFunction(x) {
  if (x > 0) {
    for (let i = 0; i < x; i++) {
      if (i % 2 === 0) {
        console.log(i);
      } else {
        console.warn(i);
      }
    }
  } else {
    throw new Error('Invalid input');
  }
  return x * 2;
}

class TestClass {
  constructor(value) {
    this.value = value;
  }
  
  getValue() {
    return this.value;
  }
}
      `;

      await fs.writeFile(testFile, complexCode);

      // First verify the file exists
      const fileExists = await fs.pathExists(testFile);
      expect(fileExists).toBe(true);

      const analysisResult = await analyzeCodeTool!.execute({
        filePath: testFile, // Use full path
        analysisType: 'all',
      }, mockContext);

      if (!analysisResult.success) {
        console.error('Analysis failed:', analysisResult.message, analysisResult.error);
        console.error('Full result:', JSON.stringify(analysisResult, null, 2));
        console.error('Test file path:', testFile);
        console.error('Working directory:', mockContext.workingDirectory);
        console.error('File exists:', fileExists);
      }

      expect(analysisResult.success).toBe(true);
      const analysisData = analysisResult.data as {
        complexity?: { cyclomaticComplexity?: number };
        patterns?: unknown;
        dependencies?: unknown;
      };
      expect(analysisData?.complexity).toBeDefined();
      expect(analysisData?.patterns).toBeDefined();
      expect(analysisData?.dependencies).toBeDefined();
      expect(analysisData?.complexity?.cyclomaticComplexity).toBeGreaterThan(1);
    });
  });

  describe('System Information', () => {
    it('should retrieve system information', async () => {
      const systemInfoTool = toolRegistry.getTool('get_system_info');
      expect(systemInfoTool).toBeDefined();

      const systemResult = await systemInfoTool!.execute({
        includeEnv: false,
        includeProcesses: true,
      }, mockContext);

      expect(systemResult.success).toBe(true);
      const systemData = systemResult.data as {
        platform?: unknown;
        memory?: unknown;
        cpu?: unknown;
        process?: unknown;
      };
      expect(systemData?.platform).toBeDefined();
      expect(systemData?.memory).toBeDefined();
      expect(systemData?.cpu).toBeDefined();
      expect(systemData?.process).toBeDefined();
    });
  });

  describe('JSON Validation', () => {
    it('should validate valid JSON', async () => {
      const validateJsonTool = toolRegistry.getTool('validate_json');
      expect(validateJsonTool).toBeDefined();

      const validJson = '{"name": "test", "value": 123}';

      const validationResult = await validateJsonTool!.execute({
        jsonContent: validJson,
        format: true,
      }, mockContext);

      expect(validationResult.success).toBe(true);
      const validationData = validationResult.data as { valid?: boolean; formatted?: string };
      expect(validationData?.valid).toBe(true);
      expect(validationData?.formatted).toBeDefined();
    });

    it('should detect invalid JSON', async () => {
      const validateJsonTool = toolRegistry.getTool('validate_json');
      expect(validateJsonTool).toBeDefined();

      const invalidJson = '{"name": "test", "value": 123'; // Missing closing brace

      const validationResult = await validateJsonTool!.execute({
        jsonContent: invalidJson,
      }, mockContext);

      expect(validationResult.success).toBe(false);
      const invalidValidationData = validationResult.data as { valid?: boolean; error?: string };
      expect(invalidValidationData?.valid).toBe(false);
      expect(invalidValidationData?.error).toBeDefined();
    });
  });

  describe('Project Context', () => {
    it('should retrieve project context', async () => {
      const getProjectContextTool = toolRegistry.getTool('get_project_context');
      expect(getProjectContextTool).toBeDefined();

      const contextResult = await getProjectContextTool!.execute({
        includeFileContent: false,
      }, mockContext);

      expect(contextResult.success).toBe(true);
      const contextData = contextResult.data as { workingDirectory?: string; projectType?: string };
      expect(contextData?.workingDirectory).toBe(tempDir);
      expect(contextData?.projectType).toBe('nodejs');
    });
  });

  describe('Session Information', () => {
    it('should retrieve session information', async () => {
      const getSessionInfoTool = toolRegistry.getTool('get_session_info');
      expect(getSessionInfoTool).toBeDefined();

      const sessionResult = await getSessionInfoTool!.execute({
        includeMessages: false,
      }, mockContext);

      expect(sessionResult.success).toBe(true);
      const sessionData = sessionResult.data as { sessionId?: string; workingDirectory?: string };
      expect(sessionData?.sessionId).toBe(mockContext.sessionId);
      expect(sessionData?.workingDirectory).toBe(tempDir);
    });
  });
});
