import {
  PromptTemplate,
  PromptContext,
  PromptSystemConfig,
  PromptExecution,
  ExecutionContext,
  ProjectType,
} from '@/types';
import { logger } from '@/utils/logger';
import { config } from '@/config';
import fs from 'fs-extra';
import path from 'path';

// Import prompt.json integration
import { promptSystemMessage, promptUserMessage, promptConfig } from '@/tools/prompt-integration';

export class PromptSystem {
  private static instance: PromptSystem;
  private templates: Map<string, PromptTemplate> = new Map();
  private config: PromptSystemConfig;
  private enabled: boolean = true;

  private constructor() {
    this.config = {
      enabled: true,
      defaultTemplate: 'cursor-system',
      autoInjectContext: true,
      maxContextSize: 8000,
      templateDirectory: path.join(process.cwd(), 'prompts'),
      customTemplates: [],
      behaviorRules: {
        communication: [],
        toolCalling: [],
        codeChanges: [],
        debugging: [],
        apiUsage: [],
      },
    };

    this.initializeDefaultTemplates();
    this.loadPromptJsonTemplates();
  }

  public static getInstance(): PromptSystem {
    if (!PromptSystem.instance) {
      PromptSystem.instance = new PromptSystem();
    }
    return PromptSystem.instance;
  }

  private initializeDefaultTemplates(): void {
    // Default cursor-system template
    const cursorSystemTemplate: PromptTemplate = {
      id: 'cursor-system',
      name: 'Cursor System Prompt',
      description: 'Default system prompt for Cursor IDE compatibility',
      content: `You are an AI coding assistant integrated with a powerful CLI tool system.

You have access to comprehensive tools for:
- File operations (read, write, search, analyze)
- Directory exploration and management  
- Shell command execution
- Code analysis and pattern detection
- Project context understanding

Guidelines:
1. Always use appropriate tools to gather information before making changes
2. Be concise and professional in your responses
3. Format code snippets clearly with proper syntax highlighting
4. Explain your reasoning when making significant changes
5. Ask for clarification when requirements are ambiguous

Current context:
- OS: {{userInfo.os}}
- Shell: {{userInfo.shell}}
- Workspace: {{userInfo.workspace}}
- Project Type: {{projectInfo.type}}
- Session: {{sessionInfo.id}}`,
      variables: [],
      category: 'system',
      version: '1.0.0',
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {},
    };

    this.templates.set('cursor-system', cursorSystemTemplate);
  }

  private loadPromptJsonTemplates(): void {
    if (promptSystemMessage) {
      // Create a template from prompt.json system message
      const promptJsonTemplate: PromptTemplate = {
        id: 'prompt-json-system',
        name: 'Prompt.json System Template',
        description: 'System prompt loaded from prompt.json configuration',
        content: promptSystemMessage,
        variables: [],
        category: 'system',
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {
          source: 'prompt.json',
          model: promptConfig?.model,
          temperature: promptConfig?.temperature,
        },
      };

      this.templates.set('prompt-json-system', promptJsonTemplate);
      
      // Set as default if available
      this.config.defaultTemplate = 'prompt-json-system';
      
      logger.info('Loaded system prompt from prompt.json', {
        templateId: 'prompt-json-system',
        contentLength: promptSystemMessage.length,
      }, 'PromptSystem');
    }

    if (promptUserMessage) {
      // Create a template from prompt.json user message
      const promptJsonUserTemplate: PromptTemplate = {
        id: 'prompt-json-user',
        name: 'Prompt.json User Template',
        description: 'User prompt loaded from prompt.json configuration',
        content: promptUserMessage,
        variables: [],
        category: 'system',
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {
          source: 'prompt.json',
        },
      };

      this.templates.set('prompt-json-user', promptJsonUserTemplate);
      
      logger.info('Loaded user prompt from prompt.json', {
        templateId: 'prompt-json-user',
        contentLength: promptUserMessage.length,
      }, 'PromptSystem');
    }
  }

  public isEnabled(): boolean {
    return this.enabled && this.config.enabled;
  }

  public enable(): void {
    this.enabled = true;
    logger.info('Prompt system enabled', undefined, 'PromptSystem');
  }

  public disable(): void {
    this.enabled = false;
    logger.info('Prompt system disabled', undefined, 'PromptSystem');
  }

  public getTemplate(templateId: string): PromptTemplate | undefined {
    return this.templates.get(templateId);
  }

  public getAllTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values());
  }

  public buildContextFromExecution(executionContext: ExecutionContext): PromptContext {
    const context: PromptContext = {
      userInfo: {
        os: `${process.platform} ${process.arch}`,
        shell: process.env['SHELL'] || process.env['ComSpec'] || 'unknown',
        workspace: executionContext.workingDirectory,
      },
      projectInfo: {
        type: executionContext.projectContext.type,
        structure: executionContext.projectContext.structure,
        dependencies: executionContext.projectContext.dependencies,
        configuration: executionContext.projectContext.configuration,
      },
      sessionInfo: {
        id: executionContext.sessionId,
        messageCount: 0, // This would be populated from session manager
        toolCallCount: 0, // This would be populated from session manager
        uptime: process.uptime(),
      },
      fileContext: {
        openFiles: [],
        recentFiles: [],
      },
      variables: {},
    };

    return context;
  }

  public async renderPrompt(
    templateId: string,
    context: PromptContext,
    variables: Record<string, unknown>
  ): Promise<string> {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    try {
      let rendered = template.content;

      // Simple template variable replacement
      // Replace {{variable.path}} with actual values
      rendered = rendered.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
        const value = this.getNestedValue(context, path) || this.getNestedValue(variables, path);
        return value !== undefined ? String(value) : match;
      });

      const execution: PromptExecution = {
        templateId,
        context,
        variables,
        renderedPrompt: rendered,
        timestamp: new Date(),
        success: true,
        metadata: {
          templateVersion: template.version,
          contentLength: rendered.length,
        },
      };

      logger.debug('Prompt rendered successfully', {
        templateId,
        contentLength: rendered.length,
      }, 'PromptSystem');

      return rendered;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to render prompt: ${templateId}`, error, 'PromptSystem');
      
      const execution: PromptExecution = {
        templateId,
        context,
        variables,
        renderedPrompt: '',
        timestamp: new Date(),
        success: false,
        error: errorMessage,
        metadata: {},
      };

      throw error;
    }
  }

  private getNestedValue(obj: any, path: string): unknown {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  public getConfig(): PromptSystemConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<PromptSystemConfig>): void {
    this.config = { ...this.config, ...updates };
    logger.info('Prompt system configuration updated', updates, 'PromptSystem');
  }

  public addTemplate(template: PromptTemplate): void {
    this.templates.set(template.id, template);
    logger.info(`Template added: ${template.id}`, {
      name: template.name,
      category: template.category,
    }, 'PromptSystem');
  }

  public removeTemplate(templateId: string): boolean {
    const removed = this.templates.delete(templateId);
    if (removed) {
      logger.info(`Template removed: ${templateId}`, undefined, 'PromptSystem');
    }
    return removed;
  }

  // Get the integrated system prompt from prompt.json
  public getIntegratedSystemPrompt(): string {
    if (promptSystemMessage) {
      return promptSystemMessage;
    }
    
    // Fallback to default template
    const defaultTemplate = this.getTemplate(this.config.defaultTemplate);
    return defaultTemplate?.content || '';
  }

  // Get the integrated user prompt from prompt.json
  public getIntegratedUserPrompt(): string {
    return promptUserMessage || '';
  }

  // Check if prompt.json integration is available
  public hasPromptJsonIntegration(): boolean {
    return !!(promptSystemMessage || promptUserMessage);
  }
}

// Export singleton instance
export const promptSystem = PromptSystem.getInstance();
